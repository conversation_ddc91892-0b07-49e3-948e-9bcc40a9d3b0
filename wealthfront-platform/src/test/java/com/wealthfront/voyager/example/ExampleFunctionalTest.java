package com.wealthfront.voyager.example;

import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.exceptions.UnauthenticatedQueryException;
import com.kaching.platform.testing.WExpectations;
import com.kaching.user.UserId;
import com.kaching.util.id.External;
import com.kaching.util.id.Flex;
import com.kaching.util.id.FlexId;
import com.wealthfront.voyager.VoyagerTestBase;
import com.wealthfront.voyager.example.navigation.MortgageRoute;
import com.wealthfront.voyager.example.navigation.MortgageStepArguments;
import com.wealthfront.voyager.example.payload.ExampleMortgageResult;
import com.wealthfront.voyager.example.payload.StateUpdateRequest;
import com.wealthfront.voyager.example.payload.StringUpdateRequest;
import com.wealthfront.voyager.example.queries.ExampleStartMortgageApplication;
import com.wealthfront.voyager.example.views.ExampleStartView;
import com.wealthfront.voyager.example.views.ExampleSubflowAView;
import com.wealthfront.voyager.example.views.ExampleView;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.wealthfront.voyager.model.VoyagerType;
import com.wealthfront.voyager.navigation.VoyagerStepId;

public class ExampleFunctionalTest extends VoyagerTestBase {

  @Test
  public void testNavigateAndBack() {
    UserId userId = new UserId(1L);
    Flex<UserId> userIdFlex = new Flex<>(userId.getId());

    mockery.checking(new WExpectations() {{
      oneOf(userIdExternalizer).internalize(userIdFlex);
      will(returnValue(userId));
    }});

    ExampleMortgageResult response = startMortgageApplication(userIdFlex);
    assertEquals(VoyagerStepId.of("application"), response.getStepId());
    assertEquals(ExampleStartView.class, response.getView().getClass());
    assertEquals(VoyagerType.MORTGAGE_APPLICATION, response.getVoyagerType());
    assertEquals("application", response.getPageViewMetricName());
    assertEquals("mortgage_application", response.getPageViewMetricCategory());

    Id<VoyagerRecord> intentId = transacter.executeWithReadOnlySessionExpression(
        session -> session.createCriteria(VoyagerRecord.class).uniqueResult().getId());

    response = processVoyagerUpdate(new FlexId<>(intentId), stringUpdateRequest(VoyagerStepId.of("application")));
    assertEquals(VoyagerStepId.of("applications/5/loan-type"), response.getStepId());
    assertEquals(ExampleView.class, response.getView().getClass());
    assertEquals(VoyagerType.MORTGAGE_APPLICATION, response.getVoyagerType());
    assertEquals("applications/{applicationId}/loan-type", response.getPageViewMetricName());
    assertEquals("mortgage_application", response.getPageViewMetricCategory());

    response = processVoyagerUpdate(new FlexId<>(intentId), processingRequest(response.getStepId()));
    assertEquals(VoyagerStepId.of("applications/5/journey-stage/2"), response.getStepId());
    assertEquals(ExampleSubflowAView.class, response.getView().getClass());

    response = processVoyagerResume(new FlexId<>(intentId));
    assertEquals(VoyagerStepId.of("applications/5/journey-stage/2"), response.getStepId());
    assertEquals(ExampleSubflowAView.class, response.getView().getClass());

    response = processResumeToStep(new FlexId<>(intentId), VoyagerStepId.of("applications/5/loan-type"));
    assertEquals(VoyagerStepId.of("applications/5/loan-type"), response.getStepId());
    assertEquals(ExampleView.class, response.getView().getClass());

    response = processResumeToStep(new FlexId<>(intentId), VoyagerStepId.of("application"));
    assertEquals(VoyagerStepId.of("application"), response.getStepId());
    assertEquals(ExampleStartView.class, response.getView().getClass());

    response = processVoyagerUpdate(new FlexId<>(intentId), stringUpdateRequest(response.getStepId()));
    assertEquals(VoyagerStepId.of("applications/5/loan-type"), response.getStepId());
    assertEquals(ExampleView.class, response.getView().getClass());

    response = processResumeToStep(new FlexId<>(intentId), VoyagerStepId.of("application"));
    assertEquals(VoyagerStepId.of("application"), response.getStepId());
    assertEquals(ExampleStartView.class, response.getView().getClass());

    response = processVoyagerUpdate(new FlexId<>(intentId), stringUpdateRequest(response.getStepId()));
    assertEquals(VoyagerStepId.of("applications/5/loan-type"), response.getStepId());
    assertEquals(ExampleView.class, response.getView().getClass());

    response = processVoyagerUpdate(new FlexId<>(intentId), processingRequest(response.getStepId()));
    assertEquals(VoyagerStepId.of("applications/5/journey-stage/2"), response.getStepId());
    assertEquals(ExampleSubflowAView.class, response.getView().getClass());

    response = processVoyagerUpdate(new FlexId<>(intentId),
        new StateUpdateRequest(VoyagerStepId.of("applications/5/loan-type"),
        ExampleMortgageApplication.State.TERMINATED));
    assertEquals(VoyagerStepId.of("applications/5/cancelled"), response.getStepId());
    assertEquals(ExampleView.class, response.getView().getClass());

    response = processResumeToStep(new FlexId<>(intentId), VoyagerStepId.of("application"));
    assertEquals(VoyagerStepId.of("applications/5/cancelled"), response.getStepId());
    assertEquals(ExampleView.class, response.getView().getClass());
  }

  @Test
  public void testExpiredRoute() {
    UserId userId = new UserId(1L);
    Flex<UserId> userIdFlex = new Flex<>(userId.getId());

    mockery.checking(new WExpectations() {{
      oneOf(userIdExternalizer).internalize(userIdFlex);
      will(returnValue(userId));
    }});

    ExampleMortgageResult response = startMortgageApplication(userIdFlex);
    assertEquals(VoyagerStepId.of("application"), response.getStepId());
    assertEquals(ExampleStartView.class, response.getView().getClass());

    Id<VoyagerRecord> intentId = transacter.executeWithReadOnlySessionExpression(
        session -> session.createCriteria(VoyagerRecord.class).uniqueResult().getId());

    response = processResumeToStep(new FlexId<>(intentId), VoyagerStepId.of("application/5/loan-type"));
    assertEquals(VoyagerStepId.of("applications/5/loan-type"), response.getStepId());
    assertEquals(ExampleView.class, response.getView().getClass());

  }

  @Test
  public void testOwnedUserApplicationArguments() {
    UserId userId = new UserId(1L);
    setAuthorizedUserId(userId);
    Flex<UserId> userIdFlex = new Flex<>(userId.getId());

    mockery.checking(new WExpectations() {{
      oneOf(userIdExternalizer).internalize(userIdFlex);
      will(returnValue(userId));
    }});

    ExampleMortgageResult response = startMortgageApplication(userIdFlex);
    assertEquals(VoyagerStepId.of("application"), response.getStepId());
    assertEquals(ExampleStartView.class, response.getView().getClass());

    Id<VoyagerRecord> intentId = transacter.executeWithReadOnlySessionExpression(
        session -> session.createCriteria(VoyagerRecord.class).uniqueResult().getId());

    MortgageStepArguments.UserApplicationArguments args = new MortgageStepArguments.UserApplicationArguments(5L, new External<>(String.valueOf(userId.getId())));
    VoyagerStepId userAppStepId = VoyagerStepId.of(MortgageRoute.USER_APPLICATION, args);

    response = processResumeToStep(new FlexId<>(intentId), userAppStepId);
    assertEquals(userAppStepId, response.getStepId());
    assertEquals(ExampleView.class, response.getView().getClass());
  }

  @Test
  public void testOwnedUserApplicationArguments_unauthorizedUserTriesToResumeOtherApp() {
    UserId authorizedUser = new UserId(1L);
    Flex<UserId> userIdFlex = new Flex<>(authorizedUser.getId());

    mockery.checking(new WExpectations() {{
      oneOf(userIdExternalizer).internalize(userIdFlex);
      will(returnValue(authorizedUser));
    }});

    ExampleMortgageResult response = startMortgageApplication(userIdFlex);
    assertEquals(VoyagerStepId.of("application"), response.getStepId());
    assertEquals(ExampleStartView.class, response.getView().getClass());

    Id<VoyagerRecord> intentId = transacter.executeWithReadOnlySessionExpression(
        session -> session.createCriteria(VoyagerRecord.class).uniqueResult().getId());

    UserId unauthorizedUser = new UserId(2L);
    setAuthorizedUserId(authorizedUser);
    MortgageStepArguments.UserApplicationArguments args = new MortgageStepArguments.UserApplicationArguments(5L, new External<>(String.valueOf(unauthorizedUser.getId())));
    VoyagerStepId userAppStepId = VoyagerStepId.of(MortgageRoute.USER_APPLICATION, args);

    assertThrows(UnauthenticatedQueryException.class, "Unauthorized access to owned resource in VoyagerStepArguments",
        () -> processResumeToStep(new FlexId<>(intentId), userAppStepId));
  }

  private StateUpdateRequest processingRequest(VoyagerStepId stepId) {
    return new StateUpdateRequest(stepId, ExampleMortgageApplication.State.PROCESSING);
  }

  private StringUpdateRequest stringUpdateRequest(VoyagerStepId stepId) {
    return new StringUpdateRequest(stepId, "Hello World");
  }

  private ExampleMortgageResult startMortgageApplication(Flex<UserId> userFlexId) {
    ExampleStartMortgageApplication query = new ExampleStartMortgageApplication(userFlexId);
    injector().injectMembers(query);
    return query.process();
  }

}