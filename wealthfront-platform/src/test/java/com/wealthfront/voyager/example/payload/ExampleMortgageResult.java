package com.wealthfront.voyager.example.payload;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.voyager.example.views.ExampleMortgageView;
import com.wealthfront.voyager.navigation.AbstractVoyagerResult;

@Entity
@ExposeType(ExposeTo.API_SERVER)
public class ExampleMortgageResult extends AbstractVoyagerResult<ExampleMortgageView> {

  @Value
  ExampleMortgageView view;

  public ExampleMortgageResult(ExampleMortgageView view) {
    this.view = view;
  }

  @Override
  public ExampleMortgageView getView() {
    return view;
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {

    private ExampleMortgageView view;
    private String pageViewMetricName;
    private String pageViewMetricCategory;

    public Builder withView(ExampleMortgageView view) {
      this.view = view;
      return this;
    }

    public Builder withPageViewMetricName(String pageViewMetricName) {
      this.pageViewMetricName = pageViewMetricName;
      return this;
    }

    public Builder withPageViewMetricCategory(String pageViewMetricCategory) {
      this.pageViewMetricCategory = pageViewMetricCategory;
      return this;
    }

    public ExampleMortgageResult build() {
      ExampleMortgageResult result = new ExampleMortgageResult(view);
      if (pageViewMetricName != null) {
        result.setPageViewMetricNameForTesting(pageViewMetricName);
      }
      if (pageViewMetricCategory != null) {
        result.setPageViewMetricCategoryForTesting(pageViewMetricCategory);
      }
      return result;
    }

  }
}
