package com.wealthfront.voyager.navigation;

import static com.kaching.DefaultKachingMarshallers.createMarshaller;
import static com.twolattes.json.Json.object;
import static com.wealthfront.test.Assert.assertMarshalling;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.kaching.util.id.ExternalId;
import com.twolattes.json.Entity;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;
import com.wealthfront.voyager.example.views.ExampleMortgageView;
import com.wealthfront.voyager.example.views.ExampleView;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.wealthfront.voyager.model.VoyagerType;

public class AbstractVoyagerResultTest {

  private static final Marshaller<ExampleVoyagerResult> marshaller = createMarshaller(ExampleVoyagerResult.class);

  @Test
  public void marshalling() {
    VoyagerStepId stepId = VoyagerStepId.of("applications/1/loan-type");
    ExampleVoyagerResult result = new ExampleVoyagerResult(new ExampleView("Hello"));
    ExternalId<VoyagerRecord> voyagerRecordId = new ExternalId<>("1");

    result.setVoyagerRecordIdForTesting(voyagerRecordId);
    result.setStepIdForTesting(stepId);
    result.setStepPathForTesting("applications/{applicationId}/loan-type");
    result.setVoyagerTypeForTesting(VoyagerType.MORTGAGE_APPLICATION);
    result.setPageViewMetricOverride("custom-category", "custom-metric-name");

    Json.Value expected = object(
        "voyagerRecordId", "1",
        "stepId", "applications/1/loan-type",
        "stepPath", "applications/{applicationId}/loan-type",
        "voyagerType", "MORTGAGE_APPLICATION",
        "pageViewMetricName", "custom-metric-name",
        "pageViewMetricCategory", "custom-category",
        "view", object(
            "exampleField", "Hello",
            "type", "example-view"
        )
    );

    assertMarshalling(marshaller, expected, result);
  }

  @Test
  public void marshalling_withoutOptional() {
    VoyagerStepId stepId = VoyagerStepId.of("applications/1/loan-type");
    ExampleVoyagerResult result = new ExampleVoyagerResult(new ExampleView("Hello"));
    ExternalId<VoyagerRecord> voyagerRecordId = new ExternalId<>("1");

    result.setVoyagerRecordIdForTesting(voyagerRecordId);
    result.setStepIdForTesting(stepId);
    result.setStepPathForTesting("");
    result.setVoyagerTypeForTesting(VoyagerType.MORTGAGE_APPLICATION);
    result.setPageViewMetricOverride("mortgage_application", "default-metric-name");

    Json.Value expected = object(
        "voyagerRecordId", "1",
        "stepId", "applications/1/loan-type",
        "stepPath", "",
        "voyagerType", "MORTGAGE_APPLICATION",
        "pageViewMetricName", "default-metric-name",
        "pageViewMetricCategory", "mortgage_application",
        "view", object(
            "exampleField", "Hello",
            "type", "example-view"
        )
    );

    assertMarshalling(marshaller, expected, result);
  }

  @Test
  public void getters() {
    ExampleView exampleView = new ExampleView();
    VoyagerStepId stepId = VoyagerStepId.of("applications/1/loan-type");
    ExampleVoyagerResult result = new ExampleVoyagerResult(exampleView);
    ExternalId<VoyagerRecord> voyagerRecordId = new ExternalId<>("1");

    result.setVoyagerRecordIdForTesting(voyagerRecordId);
    result.setStepIdForTesting(stepId);
    result.setStepPathForTesting("applications/{applicationId}/loan-type");
    result.setVoyagerTypeForTesting(VoyagerType.MORTGAGE_APPLICATION);
    result.setPageViewMetricOverride("test-metric-name", "test-category");

    assertEquals(stepId, result.getStepId());
    assertEquals("applications/{applicationId}/loan-type", result.getStepPath());
    assertEquals(voyagerRecordId, result.getVoyagerRecordId());
    assertEquals(exampleView, result.getView());
    assertEquals(VoyagerType.MORTGAGE_APPLICATION, result.getVoyagerType());
    assertEquals("test-metric-name", result.getPageViewMetricName());
    assertEquals("test-category", result.getPageViewMetricCategory());
  }

  @Test
  public void getters_withoutOptional() {
    ExampleView exampleView = new ExampleView();
    VoyagerStepId stepId = VoyagerStepId.of("applications/1/loan-type");
    ExampleVoyagerResult result = new ExampleVoyagerResult(exampleView);
    ExternalId<VoyagerRecord> voyagerRecordId = new ExternalId<>("1");

    result.setVoyagerRecordIdForTesting(voyagerRecordId);
    result.setStepIdForTesting(stepId);
    result.setStepPathForTesting("");
    result.setVoyagerTypeForTesting(VoyagerType.NEW_ACCOUNT);
    result.setPageViewMetricOverride("new_account", "another-metric-name");

    assertEquals(stepId, result.getStepId());
    assertEquals("", result.getStepPath());
    assertEquals(voyagerRecordId, result.getVoyagerRecordId());
    assertEquals(exampleView, result.getView());
    assertEquals(VoyagerType.NEW_ACCOUNT, result.getVoyagerType());
    assertEquals("another-metric-name", result.getPageViewMetricName());
    assertEquals("new_account", result.getPageViewMetricCategory());
  }

  @Test
  public void setVoyagerMetadata_conditionalFields() {
    ExampleView exampleView = new ExampleView();
    VoyagerStepId stepId = VoyagerStepId.of("applications/1/loan-type");
    ExampleVoyagerResult result = new ExampleVoyagerResult(exampleView);
    ExternalId<VoyagerRecord> voyagerRecordId = new ExternalId<>("1");

    result.setVoyagerMetadata(voyagerRecordId, stepId, "applications/{applicationId}/loan-type", VoyagerType.MORTGAGE_APPLICATION);

    assertEquals(stepId, result.getStepId());
    assertEquals(voyagerRecordId, result.getVoyagerRecordId());
    assertEquals("applications/{applicationId}/loan-type", result.getStepPath());
    assertEquals(VoyagerType.MORTGAGE_APPLICATION, result.getVoyagerType());
    assertEquals("applications/{applicationId}/loan-type", result.getPageViewMetricName());
    assertEquals("mortgage_application", result.getPageViewMetricCategory());
  }

  @Test
  public void setVoyagerMetadata_preservesExistingFields() {
    ExampleView exampleView = new ExampleView();
    VoyagerStepId stepId = VoyagerStepId.of("applications/1/loan-type");
    ExampleVoyagerResult result = new ExampleVoyagerResult(exampleView);
    ExternalId<VoyagerRecord> voyagerRecordId = new ExternalId<>("1");

    result.setPageViewMetricOverride("existing-category", "existing-metric-name");

    result.setVoyagerMetadata(voyagerRecordId, stepId, "applications/{applicationId}/loan-type", VoyagerType.MORTGAGE_APPLICATION);

    assertEquals(stepId, result.getStepId());
    assertEquals(voyagerRecordId, result.getVoyagerRecordId());
    assertEquals("applications/{applicationId}/loan-type", result.getStepPath());
    assertEquals(VoyagerType.MORTGAGE_APPLICATION, result.getVoyagerType());
    assertEquals("existing-metric-name", result.getPageViewMetricName());
    assertEquals("existing-category", result.getPageViewMetricCategory());
  }

  @Entity
  static class ExampleVoyagerResult extends AbstractVoyagerResult<ExampleMortgageView> {

    @Value
    private ExampleMortgageView view;

    ExampleVoyagerResult() { /* JSON */ }

    ExampleVoyagerResult(ExampleMortgageView view) {
      this.view = view;
    }

    @Override
    public ExampleMortgageView getView() {
      return view;
    }

  }

}